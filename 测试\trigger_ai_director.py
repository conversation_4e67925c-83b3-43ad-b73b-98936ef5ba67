#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的AI导演触发脚本
向浏览器中的testuser发送AI导演响应
"""

from openai import OpenAI
import time

def get_ai_director_response():
    """获取AI导演响应"""
    try:
        # 智匠MindCraft配置
        base_url = "https://api.mindcraft.com.cn/v1"
        api_key = "MC-94D4CC750E92436FB3FA51C9F41D03A9"
        model = "deepseek-r1-siliconflow"
        
        client = OpenAI(base_url=base_url, api_key=api_key)
        
        # 模拟修炼突破事件
        event_context = """
        【游戏事件】修炼突破事件
        玩家：testuser
        事件：成功从炼气期突破到筑基期
        描述：玩家在修炼《九阳神功》时，感受到体内真气汹涌，天地灵气向其汇聚，成功突破境界
        地点：Limbo
        时间：{current_time}
        """.format(current_time=time.strftime("%Y-%m-%d %H:%M:%S"))
        
        # AI导演提示词
        messages = [
            {
                "role": "system",
                "content": """你是一个仙侠MUD游戏的AI导演，负责根据玩家的游戏事件生成精彩的剧情响应。
                你需要：
                1. 以古典仙侠风格描述场景变化
                2. 添加神秘的天地异象
                3. 暗示未来的机遇或挑战
                4. 保持50-100字的简洁描述
                请用中文回复，语言要优美古典。"""
            },
            {
                "role": "user", 
                "content": event_context
            }
        ]
        
        print("🎭 正在调用AI导演...")
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=0.7,
            max_tokens=200
        )
        
        print(f"Debug - 完整响应: {response}")
        
        if response and response.choices and len(response.choices) > 0:
            ai_response = response.choices[0].message.content.strip()
            print(f"✅ AI导演响应: {ai_response}")
            return ai_response
        else:
            print("❌ 响应格式异常")
            return "天地灵气汇聚，异象乍现...【AI响应异常】"
        
    except Exception as e:
        print(f"❌ AI调用失败: {e}")
        return "天地灵气汇聚，异象乍现，似有大事将发生...【AI导演暂不可用】"

def send_message_to_browser():
    """向浏览器发送消息的简化版本"""
    ai_response = get_ai_director_response()
    
    # 这里我们暂时只能打印，实际游戏中这些消息会发送到浏览器
    print("\n" + "="*60)
    print("🎭 【AI导演响应】")
    print("="*60)
    print(ai_response)
    print("="*60)
    
    # 提示用户在浏览器中查看
    print("\n💡 请在游戏浏览器中查看AI导演的响应消息！")
    print("   如果看不到消息，请尝试刷新或重新连接。")
    
    return ai_response

if __name__ == "__main__":
    send_message_to_browser() 