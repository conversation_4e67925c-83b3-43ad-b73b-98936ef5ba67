{"permissions": {"allow": ["WebFetch(domain:store.steampowered.com)", "Bash(find:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(source:*)", "Bash(. venv/bin/activate)", "Bash(venv/bin/python:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__sequential-thinking__sequentialthinking", "Bash(git add:*)", "Bash(git config:*)", "Bash(grep:*)", "Bash(evennia info:*)"], "deny": []}}